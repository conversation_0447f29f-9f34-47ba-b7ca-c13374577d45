"""create_data_source_oauth_bindings_table

Revision ID: e13fd5ed20b5
Revises: 34546a113f9b
Create Date: 2025-06-04 16:16:29.706327

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

import models as models

# revision identifiers, used by Alembic.
revision = 'e13fd5ed20b5'
down_revision = '34546a113f9b'
branch_labels = None
depends_on = None


def upgrade():
    # Create data_source_oauth_bindings table
    op.create_table('data_source_oauth_bindings',
        sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
        sa.Column('tenant_id', postgresql.UUID(), nullable=False),
        sa.Column('access_token', sa.String(length=255), nullable=False),
        sa.Column('provider', sa.String(length=255), nullable=False),
        sa.Column('source_info', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('disabled', sa.Boolean(), server_default=sa.text('false'), nullable=True),
        sa.PrimaryKeyConstraint('id', name='source_binding_pkey')
    )
    with op.batch_alter_table('data_source_oauth_bindings', schema=None) as batch_op:
        batch_op.create_index('source_binding_tenant_id_idx', ['tenant_id'], unique=False)
        batch_op.create_index('source_info_idx', ['source_info'], unique=False, postgresql_using='gin')


def downgrade():
    # Drop data_source_oauth_bindings table
    with op.batch_alter_table('data_source_oauth_bindings', schema=None) as batch_op:
        batch_op.drop_index('source_info_idx', postgresql_using='gin')
        batch_op.drop_index('source_binding_tenant_id_idx')

    op.drop_table('data_source_oauth_bindings')
