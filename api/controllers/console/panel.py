from datetime import datetime, timed<PERSON>ta

from flask import jsonify
from flask_restful import Resource, reqparse
from sqlalchemy import func

from extensions.ext_database import db
from models.account import Account
from models.dataset import AppDatasetJoin, Dataset, Document, DocumentSegment
from models.db_info import DBinfo
from models.model import App, Conversation, Message

from . import api


class DataSetPanel(Resource):
    def get(self):
        total_datases = db.session.query(Dataset).filter(
            Dataset.data_source_type != 'sql'
        ).count()
        total_docouments = db.session.query(Document).filter(
            Document.data_source_type != 'sql'
        ).count()

        total_docouments_archived = db.session.query(Document).filter(
            Document.data_source_type != 'sql',
            Document.archived == True
        ).count()

        total_segments = db.session.query(DocumentSegment, Document).filter(
            Document.data_source_type != 'sql',
            DocumentSegment.document_id == Document.id
        ).count()
        return {
            'total_datases': total_datases,
            'total_docouments': total_docouments,
            'total_docouments_archived': total_docouments_archived,
            'total_segments': total_segments
        }


class InteliAssistPanel(Resource):
    def get(self):

        total_apps = db.session.query(App).filter(App.mode != 'sql').count()

        total_app_datasets_joins = db.session.query(
            AppDatasetJoin
        ).join(
            App, App.id == AppDatasetJoin.app_id
        ).join(
            Dataset, Dataset.id == AppDatasetJoin.dataset_id
        ).filter(App.mode != 'sql').count()

        return {
            'total_apps': total_apps,
            'total_app_datasets_joins': total_app_datasets_joins
        }


class TopApps(Resource):
    def get(self):
        def get_top_apps(mode, limit=5):
            query = db.session.query(
                App.name,
                func.count(Message.id).label('message_count')
            ).join(
                Message, Message.app_id == App.id

            ).group_by(
                App.name
            ).having(
                func.count(Message.id) > 0
            ).order_by(
                func.count(Message.id).desc()
            )
            if mode == 'sql':
                query = query.filter(App.mode == 'sql')
            else:
                query = query.filter(App.mode != 'sql')
            return query.limit(limit).all()

        top_apps = get_top_apps('chat')
        top_sql_apps = get_top_apps('sql')

        chat_ = [{'name': app.name, 'message_count': app.message_count}
                 for app in top_apps]
        sql_ = [{'name': app.name, 'message_count': app.message_count}
                for app in top_sql_apps]

        result = {"chat_apps": chat_, "sql_apps": sql_}
        return result


class AlertEvents(Resource):
    def get(self):
        unavailale_documents = db.session.query(Document).filter(
            Document.data_source_type != 'sql',
            Document.error.isnot(None)
        ).count()
        available_documents = db.session.query(Document).filter(
            Document.data_source_type != 'sql',
            Document.error.is_(None)
        ).count()
        available_apps = db.session.query(App).filter(
            App.mode != 'sql',
            App.status == 'normal'
        ).count()

        unavailable_apps = db.session.query(App).filter(
            App.mode != 'sql',
            App.status != 'normal'
        ).count()

        available_sql_apps = db.session.query(App).filter(
            App.mode == 'sql',
            App.status == 'normal'
        ).count()

        unavailale_sql_apps = db.session.query(App).filter(
            App.mode == 'sql',
            App.status != 'normal'
        ).count()
        return {
            'available_documents': available_documents,
            'unavailale_documents': unavailale_documents,
            'available_apps': available_apps,
            'unavailable_apps': unavailable_apps,
            'available_sql_apps': available_sql_apps,
            'unavailale_sql_apps': unavailale_sql_apps
        }


class UserStatistics(Resource):
    def get(self):
        parser = reqparse.RequestParser()
        parser.add_argument('range', choices=[
                            'daily', 'weekly', 'monthly', 'semi-annually'], required=True, location='args')
        parser.add_argument('type', choices=['user-activity', 'token-cost', 'messages-count',
                                             'tokens-per-second','dataset-activity'], required=True, location='args')
        args = parser.parse_args()
        end_time = datetime.now()

        @staticmethod
        def generate_date_range(start_date, end_date):
            date_range = []
            current_date = start_date
            while current_date <= end_date:
                date_range.append(current_date.strftime('%Y-%m-%d'))
                current_date += timedelta(days=1)
            return date_range
        @staticmethod
        def process_dataset_activity(sql_results, start_date_str, end_date_str):
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
            date_range = generate_date_range(start_date, end_date)

            data_dict = {}
            for result in sql_results:
                date = result.date
                name = result.name
                count = result.count
                if name not in data_dict:
                    data_dict[name] = {date: count}
                else:
                    data_dict[name][date] = count

            series = []
            for name, date_counts in data_dict.items():
                data = [date_counts.get(date, 0) for date in date_range]
                series.append({'name': name, 'data': data})

            return {
                'xAxis': date_range,
                'series': series
            }
        def get_time_range(range):
            if range == 'daily':
                return end_time - timedelta(days=1)
            elif range == 'weekly':
                return end_time - timedelta(days=7)
            elif range == 'monthly':
                return end_time - timedelta(days=30)
            elif range == 'semi-annually':
                return end_time - timedelta(days=180)

        start_time = get_time_range(args['range'])

        response_data = []

        if args['type'] == 'dataset-activity':
            # Build ORM query for dataset activity
            query = db.session.query(
                Dataset.name,
                func.to_char(func.date_trunc('day', Document.created_at), 'YYYY-MM-DD').label('date'),
                func.count().label('count')
            ).join(
                Document, Document.dataset_id == Dataset.id
            ).filter(
                Document.data_source_type != 'sql',
                Document.created_at >= start_time,
                Document.created_at < end_time
            ).group_by(
                Dataset.name,
                func.date_trunc('day', Document.created_at)
            ).order_by(
                func.date_trunc('day', Document.created_at)
            )

            results = query.all()
            response_data = process_dataset_activity(results, start_time.strftime('%Y-%m-%d'), end_time.strftime('%Y-%m-%d'))

        elif args['type'] == 'user-activity':
            # Build ORM query for user activity
            query = db.session.query(
                func.to_char(func.date_trunc('day', Message.created_at), 'YYYY-MM-DD').label('date'),
                func.count(func.distinct(Message.from_end_user_id)).label('count')
            ).filter(
                Message.created_at >= start_time,
                Message.created_at < end_time
            ).group_by(
                func.date_trunc('day', Message.created_at)
            ).order_by(
                func.date_trunc('day', Message.created_at)
            )

            results = query.all()
            for result in results:
                response_data.append({
                    'date': result.date,
                    'count': result.count
                })

        elif args['type'] == 'token-cost':
            # Build ORM query for token cost
            query = db.session.query(
                func.to_char(func.date_trunc('day', Message.created_at), 'YYYY-MM-DD').label('date'),
                (func.sum(Message.message_tokens) + func.sum(Message.answer_tokens)).label('count'),
                func.sum(Message.total_price).label('total_price')
            ).filter(
                Message.created_at >= start_time,
                Message.created_at < end_time
            ).group_by(
                func.date_trunc('day', Message.created_at)
            ).order_by(
                func.date_trunc('day', Message.created_at)
            )

            results = query.all()
            for result in results:
                response_data.append({
                    'date': result.date,
                    'count': result.count
                })

        elif args['type'] == 'messages-count':
            # Build ORM query for messages count with subquery
            subquery = db.session.query(
                Conversation.created_at,
                func.count(Message.id).label('message_count')
            ).join(
                Message, Conversation.id == Message.conversation_id
            ).filter(
                Conversation.override_model_configs.is_(None),
                Conversation.created_at >= start_time,
                Conversation.created_at < end_time
            ).group_by(
                Conversation.created_at
            ).subquery()

            from sqlalchemy import Numeric, cast
            query = db.session.query(
                func.to_char(func.date_trunc('day', subquery.c.created_at), 'YYYY-MM-DD').label('date'),
                func.round(cast(func.avg(subquery.c.message_count), Numeric), 2).label('count')
            ).group_by(
                func.date_trunc('day', subquery.c.created_at)
            ).order_by(
                func.date_trunc('day', subquery.c.created_at)
            )

            results = query.all()
            for result in results:
                response_data.append({
                    'date': result.date,
                    'count': result.count
                })

        elif args['type'] == 'tokens-per-second':
            # Build ORM query for tokens per second
            from sqlalchemy import Numeric, case, cast
            query = db.session.query(
                func.to_char(func.date_trunc('day', Message.created_at), 'YYYY-MM-DD').label('date'),
                case(
                    (func.sum(Message.provider_response_latency) == 0, 0),
                    else_=func.round(
                        cast(func.sum(Message.answer_tokens) / func.sum(Message.provider_response_latency), Numeric),
                        2
                    )
                ).label('count')
            ).filter(
                Message.created_at >= start_time,
                Message.created_at < end_time
            ).group_by(
                func.date_trunc('day', Message.created_at)
            ).order_by(
                func.date_trunc('day', Message.created_at)
            )

            results = query.all()
            for result in results:
                response_data.append({
                    'date': result.date,
                    'count': float(result.count) if result.count else 0
                })

        rlt = jsonify({
            'data': response_data,
            'type': args['type']
        })

        return rlt


class Text2SQLAssistPanel(Resource):
    def get(self):

        total_apps = db.session.query(App).filter(App.mode == 'sql').count()

        total_app_datasets_joins = db.session.query(
            AppDatasetJoin
        ).join(
            App, App.id == AppDatasetJoin.app_id
        ).join(
            Dataset, Dataset.id == AppDatasetJoin.dataset_id
        ).filter(App.mode == 'sql').count()

        return {
            'total_apps': total_apps,
            'total_app_datasets_joins': total_app_datasets_joins
        }

class SQLDataSetPanel(Resource):
    def get(self):
        total_datases = db.session.query(Dataset).filter(
            Dataset.data_source_type == 'sql'
        ).count()

        # Build ORM query for SQL segments by dataset
        query = db.session.query(
            Dataset.name,
            Dataset.id,
            func.count().label('count')
        ).join(
            DocumentSegment, DocumentSegment.dataset_id == Dataset.id
        ).filter(
            Dataset.data_source_type == 'sql'
        ).group_by(
            Dataset.id,
            Dataset.name
        )

        results = query.all()
        rs_data = []
        for result in results:
            rs_data.append({
                'name': result.name,
                'id': result.id,
                'count': result.count
            })

        return jsonify({
            'total_datases': total_datases,
            'total_sql_segments_by_app': rs_data
        })


class ConnectionStatistics(Resource):
    def get(self):
        total_connections=db.session.query(
            DBinfo
        ).count()

        connections_by_dbtype=db.session.query(
            DBinfo.dbtype,
            func.count(DBinfo.id).label('count')
        ).group_by(DBinfo.dbtype).all()

        result=jsonify({
            'total-connections':total_connections,
            'connections-by-dbtype':[ {connection.dbtype:connection.count} for connection in connections_by_dbtype]
        })
        return result



class DocumentStatistics(Resource):
    def get(self):
        try:

            dataset_stats = self._get_dataset_statistics()


            author_stats = self._get_author_statistics()

            return jsonify({
                'dataset_statistics': dataset_stats,
                'author_statistics': author_stats
            })
        except Exception as e:
            from controllers.console.error import DocumentStatisticsError
            raise DocumentStatisticsError(f"Failed to fetch document statistics: {str(e)}")

    def _get_dataset_statistics(self):
        """获取按数据集分组的文档统计信息"""

        from datetime import datetime, timedelta
        now = datetime.now()
        two_weeks_ago = now - timedelta(days=14)


        total_counts_query = db.session.query(
            Dataset.name,
            func.count(Document.id).label('total_count')
        ).join(
            Document, Document.dataset_id == Dataset.id
        ).group_by(
            Dataset.name
        ).subquery()

        # 查询最近两周的文档计数（按数据集分组）
        recent_counts_query = db.session.query(
            Dataset.name,
            func.count(Document.id).label('recent_count')
        ).join(
            Document, Document.dataset_id == Dataset.id
        ).filter(
            Document.created_at >= two_weeks_ago,
            Document.created_at < now
        ).group_by(
            Dataset.name
        ).subquery()

        # 合并查询结果
        results = db.session.query(
            total_counts_query.c.name,
            total_counts_query.c.total_count,
            func.coalesce(recent_counts_query.c.recent_count, 0).label('recent_count')
        ).outerjoin(
            recent_counts_query, total_counts_query.c.name == recent_counts_query.c.name
        ).order_by(
            total_counts_query.c.name
        ).all()


        return [
            {
                'name': row.name,
                'total_count': row.total_count,
                'recent_count': row.recent_count
            }
            for row in results
        ]

    def _get_author_statistics(self):
        """获取按作者分组的文档统计信息"""

        from datetime import datetime, timedelta
        now = datetime.now()
        two_weeks_ago = now - timedelta(days=14)

        # 查询所有文档计数（按作者分组）
        total_counts_query = db.session.query(
            Account.name.label('username'),
            func.count(Document.id).label('total_count')
        ).join(
            Document, Document.created_by == Account.id
        ).group_by(
            Account.name
        ).subquery()

        # 查询最近两周的文档计数（按作者分组）
        recent_counts_query = db.session.query(
            Account.name.label('username'),
            func.count(Document.id).label('recent_count')
        ).join(
            Document, Document.created_by == Account.id
        ).filter(
            Document.created_at >= two_weeks_ago,
            Document.created_at < now
        ).group_by(
            Account.name
        ).subquery()

        # 合并查询结果
        results = db.session.query(
            total_counts_query.c.username,
            total_counts_query.c.total_count,
            func.coalesce(recent_counts_query.c.recent_count, 0).label('recent_count')
        ).outerjoin(
            recent_counts_query, total_counts_query.c.username == recent_counts_query.c.username
        ).order_by(
            total_counts_query.c.username
        ).all()


        return [
            {
                'name': row.username,
                'total_count': row.total_count,
                'recent_count': row.recent_count
            }
            for row in results
        ]



api.add_resource(InteliAssistPanel, '/assistant-apps')
api.add_resource(DataSetPanel, '/assistant-datasets')
api.add_resource(TopApps, '/top-apps')
api.add_resource(AlertEvents, '/alert-events')
api.add_resource(UserStatistics, '/user-statistics')
api.add_resource(Text2SQLAssistPanel, '/text2sql-apps')
api.add_resource(SQLDataSetPanel,'/text2sql-datasets')
api.add_resource(ConnectionStatistics,'/connection-statistics')
api.add_resource(DocumentStatistics, '/document-statistics')

